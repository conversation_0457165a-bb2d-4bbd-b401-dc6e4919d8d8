{"name": "obsidian-smart-composer", "version": "1.2.1", "description": "This is a sample plugin for Obsidian (https://obsidian.md)", "main": "main.js", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "version": "node version-bump.mjs", "lint:check": "(prettier --check --cache --cache-strategy content --cache-location node_modules/.cache/.prettiercache .) && (eslint .)", "lint:fix": "(prettier --write --cache --cache-strategy content --cache-location node_modules/.cache/.prettiercache .) && (eslint --fix .)", "type:check": "tsc --noEmit", "test": "jest", "migrate:compile": "node compile-migration.js"}, "keywords": [], "author": "", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.13", "@types/lodash.debounce": "^4.0.9", "@types/lodash.isequal": "^4.5.8", "@types/node": "^16.11.6", "@types/path-browserify": "^1.0.3", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/semver": "^7.5.8", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "builtin-modules": "3.3.0", "drizzle-kit": "^0.30.3", "esbuild": "0.17.3", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.30.0", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^5.0.0", "jest": "^29.7.0", "obsidian": "latest", "prettier": "^3.3.3", "ts-jest": "^29.2.5", "tslib": "2.4.0", "typescript": "^5.6.2"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@electric-sql/pglite": "0.2.12", "@google/generative-ai": "^0.24.0", "@lexical/clipboard": "^0.17.1", "@lexical/react": "^0.17.1", "@modelcontextprotocol/sdk": "^1.9.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-query": "^5.56.2", "clsx": "^2.1.1", "dayjs": "^1.11.13", "drizzle-orm": "^0.39.0", "exponential-backoff": "^3.1.1", "fuzzysort": "^3.1.0", "groq-sdk": "^0.7.0", "js-tiktoken": "^1.0.15", "langchain": "^0.3.2", "lexical": "^0.17.1", "lodash.debounce": "^4.0.8", "lodash.isequal": "^4.5.0", "lucide-react": "^0.447.0", "minimatch": "^10.0.1", "openai": "^4.91.1", "parse5": "^7.1.2", "path-browserify": "^1.0.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "react-textarea-autosize": "^8.5.9", "remark-gfm": "^4.0.0", "shell-env": "^4.0.1", "uuid": "^10.0.0", "vscode-diff": "^2.1.1", "zod": "^3.23.8"}}