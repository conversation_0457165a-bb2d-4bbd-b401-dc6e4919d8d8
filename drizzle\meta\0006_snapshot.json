{"id": "f33fa3e2-3170-4187-a15d-d01ed651fe8b", "prevId": "410315fb-45e8-44ba-91f2-deadeac035d6", "version": "7", "dialect": "postgresql", "tables": {"public.template": {"name": "template", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"template_name_unique": {"name": "template_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "checkConstraints": {}}, "public.vector_data_openai_text_embedding_3_small": {"name": "vector_data_openai_text_embedding_3_small", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": true}, "mtime": {"name": "mtime", "type": "bigint", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1536)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {"embeddingIndex_openai_text_embedding_3_small": {"name": "embeddingIndex_openai_text_embedding_3_small", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "concurrently": false, "method": "hnsw", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.vector_data_openai_text_embedding_3_large": {"name": "vector_data_openai_text_embedding_3_large", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": true}, "mtime": {"name": "mtime", "type": "bigint", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(3072)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.vector_data_ollama_nomic_embed_text": {"name": "vector_data_ollama_nomic_embed_text", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": true}, "mtime": {"name": "mtime", "type": "bigint", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(768)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {"embeddingIndex_ollama_nomic_embed_text": {"name": "embeddingIndex_ollama_nomic_embed_text", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "concurrently": false, "method": "hnsw", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.vector_data_ollama_mxbai_embed_large": {"name": "vector_data_ollama_mxbai_embed_large", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": true}, "mtime": {"name": "mtime", "type": "bigint", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1024)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {"embeddingIndex_ollama_mxbai_embed_large": {"name": "embeddingIndex_ollama_mxbai_embed_large", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "concurrently": false, "method": "hnsw", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "public.vector_data_ollama_bge_m3": {"name": "vector_data_ollama_bge_m3", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "path": {"name": "path", "type": "text", "primaryKey": false, "notNull": true}, "mtime": {"name": "mtime", "type": "bigint", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1024)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": true}}, "indexes": {"embeddingIndex_ollama_bge_m3": {"name": "embeddingIndex_ollama_bge_m3", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "concurrently": false, "method": "hnsw", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}