# compiled output
main.js
/node_modules

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# local env files
.env
.env*.local

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
esbuild.config.mjs
jest.config.js
version-bump.mjs
versions.json
package.json
manifest.json
compile-migration.js
import-meta-url-shim.js

# Obsidian
.obsidian

# Markdown
*.md
