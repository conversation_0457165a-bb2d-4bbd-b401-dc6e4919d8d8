name: Release Obsidian plugin

on:
  push:
    tags:
      - '*'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Use Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18.x'

      - name: Set Tag variable
        run: |
          echo "TAG=${{ github.ref_name }}" >> $GITHUB_ENV

      - name: Bump version
        run: npm run version $TAG

      - name: Build plugin
        run: |
          npm install
          npm run build

      - name: Create release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          tag="${GITHUB_REF#refs/tags/}"
          gh release create "$tag" \
            --title="$tag" \
            main.js manifest.json styles.css

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          commit-message: 'version bump: ${{ env.TAG }}'
          title: 'Version bump: ${{ env.TAG }}'
          body: |
            Automated version bump after release ${{ env.TAG }}

            This PR was automatically created by the release workflow.
          branch: 'version-bump/${{ env.TAG }}'
          base: 'main'
          delete-branch: true
